#!/usr/bin/env python3
"""
Protobuf build script for MQTT device messages.

This script compiles .proto files into Python modules.
"""

import os
import subprocess
import sys
from pathlib import Path


def build_protobuf_files():
    """Build protobuf files from .proto definitions."""
    
    # Define directories
    proto_dir = Path("protos")
    output_dir = Path("proto_gen")
    
    # Create output directory if it doesn't exist
    output_dir.mkdir(exist_ok=True)
    
    # Create __init__.py in output directory
    init_file = output_dir / "__init__.py"
    if not init_file.exists():
        init_file.write_text("# Generated protobuf modules\n")
    
    # Find all .proto files
    proto_files = list(proto_dir.glob("*.proto"))
    
    if not proto_files:
        print("No .proto files found in the protos directory")
        return False
    
    print(f"Found {len(proto_files)} proto file(s):")
    for proto_file in proto_files:
        print(f"  - {proto_file}")
    
    # Check if protoc is available
    try:
        result = subprocess.run(
            ["protoc", "--version"], 
            capture_output=True, 
            text=True, 
            check=True
        )
        print(f"Using {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Error: protoc (Protocol Compiler) not found!")
        print("Please install protobuf compiler:")
        print("  Ubuntu/Debian: sudo apt-get install protobuf-compiler")
        print("  macOS: brew install protobuf")
        print("  Windows: Download from https://github.com/protocolbuffers/protobuf/releases")
        return False
    
    # Compile each proto file
    success = True
    for proto_file in proto_files:
        try:
            cmd = [
                "protoc",
                f"--proto_path={proto_dir}",
                f"--python_out={output_dir}",
                str(proto_file)
            ]
            
            print(f"Compiling {proto_file.name}...")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Generate the expected output file name
            expected_output = output_dir / f"{proto_file.stem}_pb2.py"
            if expected_output.exists():
                print(f"  ✓ Generated {expected_output}")
            else:
                print(f"  ⚠ Expected output file not found: {expected_output}")
                
        except subprocess.CalledProcessError as e:
            print(f"  ✗ Failed to compile {proto_file.name}")
            print(f"    Error: {e.stderr}")
            success = False
    
    if success:
        print("\n✓ All protobuf files compiled successfully!")
        print(f"Generated files are in: {output_dir.absolute()}")
        
        # List generated files
        generated_files = list(output_dir.glob("*_pb2.py"))
        if generated_files:
            print("\nGenerated modules:")
            for gen_file in generated_files:
                print(f"  - {gen_file.name}")
    else:
        print("\n✗ Some protobuf files failed to compile")
    
    return success


def clean_generated_files():
    """Clean up generated protobuf files."""
    output_dir = Path("proto_gen")
    
    if not output_dir.exists():
        print("No generated files to clean")
        return
    
    # Remove all generated files
    generated_files = list(output_dir.glob("*_pb2.py"))
    for gen_file in generated_files:
        gen_file.unlink()
        print(f"Removed {gen_file}")
    
    # Remove __pycache__ if it exists
    pycache_dir = output_dir / "__pycache__"
    if pycache_dir.exists():
        import shutil
        shutil.rmtree(pycache_dir)
        print(f"Removed {pycache_dir}")
    
    print(f"Cleaned up generated files in {output_dir}")


def main():
    """Main entry point."""
    if len(sys.argv) > 1 and sys.argv[1] == "clean":
        clean_generated_files()
    else:
        build_protobuf_files()


if __name__ == "__main__":
    main()
