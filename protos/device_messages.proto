syntax = "proto3";

package device_messages;

// Generic message envelope for all device communications
message GenericMessageEnvelope {
  string message_type = 1;
  string message_version = 2;
  bytes message = 3;  // Serialized inner message
  int64 sent_at = 4;  // Unix timestamp in seconds
}

// Device status message
message DeviceStatusMessage {
  string device_id = 1;
  DeviceStatus status = 2;
  int64 seq = 3;
  int64 ts = 4;  // Unix timestamp in milliseconds
  optional Location location = 5;
  optional BatteryInfo battery = 6;
  map<string, string> metadata = 7;
}

// Device status enumeration
enum DeviceStatus {
  DEVICE_STATUS_UNKNOWN = 0;
  DEVICE_STATUS_READY = 1;
  DEVICE_STATUS_IN_TRANSIT = 2;
  DEVICE_STATUS_STUCK = 3;
  DEVICE_STATUS_OFFLINE = 4;
  DEVICE_STATUS_MAINTENANCE = 5;
}

// Location information
message Location {
  double latitude = 1;
  double longitude = 2;
  optional double altitude = 3;
  optional double accuracy = 4;
  int64 timestamp = 5;
}

// Battery information
message BatteryInfo {
  double level = 1;  // Battery level 0.0 - 1.0
  bool charging = 2;
  optional double voltage = 3;
  optional double temperature = 4;
}

// Device telemetry message
message DeviceTelemetryMessage {
  string device_id = 1;
  int64 seq = 2;
  int64 ts = 3;
  repeated SensorReading sensors = 4;
  optional SystemInfo system = 5;
}

// Sensor reading
message SensorReading {
  string sensor_id = 1;
  SensorType type = 2;
  double value = 3;
  string unit = 4;
  int64 timestamp = 5;
}

// Sensor types
enum SensorType {
  SENSOR_TYPE_UNKNOWN = 0;
  SENSOR_TYPE_TEMPERATURE = 1;
  SENSOR_TYPE_HUMIDITY = 2;
  SENSOR_TYPE_PRESSURE = 3;
  SENSOR_TYPE_ACCELEROMETER = 4;
  SENSOR_TYPE_GYROSCOPE = 5;
  SENSOR_TYPE_GPS = 6;
  SENSOR_TYPE_PROXIMITY = 7;
  SENSOR_TYPE_LIGHT = 8;
}

// System information
message SystemInfo {
  double cpu_usage = 1;
  double memory_usage = 2;
  double disk_usage = 3;
  double network_usage = 4;
  int64 uptime = 5;
}

// Device command message
message DeviceCommandMessage {
  string device_id = 1;
  string command_id = 2;
  CommandType command_type = 3;
  map<string, string> parameters = 4;
  int64 timestamp = 5;
  optional int64 timeout = 6;
}

// Command types
enum CommandType {
  COMMAND_TYPE_UNKNOWN = 0;
  COMMAND_TYPE_RESTART = 1;
  COMMAND_TYPE_UPDATE_CONFIG = 2;
  COMMAND_TYPE_START_TASK = 3;
  COMMAND_TYPE_STOP_TASK = 4;
  COMMAND_TYPE_GET_STATUS = 5;
  COMMAND_TYPE_CALIBRATE = 6;
}
