from __future__ import annotations
import os
import time, json, ujson as fastjson
from dataclasses import dataclass, asdict
from typing import Any, Dict, Optional

from common.protobuf_envelope import ProtobufEnvelope, ProtobufEnvelopeError

# Configuration: Use protobuf if available and enabled
USE_PROTOBUF = os.getenv("USE_PROTOBUF", "true").lower() in ("true", "1", "yes")


@dataclass
class GenericMessageEnvelope:
    """
    Generic message envelope that can use either JSON or protobuf serialization.

    This class provides backward compatibility while supporting protobuf when available.
    """
    messageType: str
    messageVersion: str
    message: Any  # JSON string payload for JSON mode, or dict for protobuf mode
    sentAt: int

    def SerializeToString(self) -> bytes:
        """Serialize the envelope to bytes using the configured format."""
        if USE_PROTOBUF and ProtobufEnvelope.is_available():
            try:
                # For protobuf mode, message should be a dict
                if isinstance(self.message, str):
                    message_dict = json.loads(self.message)
                else:
                    message_dict = self.message

                # Create protobuf envelope based on message type
                if self.messageType == "DeviceStatus":
                    return ProtobufEnvelope.create_device_status_envelope(
                        device_id=message_dict["deviceId"],
                        status=message_dict["status"],
                        seq=message_dict["seq"],
                        ts=message_dict["ts"],
                        location=message_dict.get("location"),
                        battery=message_dict.get("battery"),
                        metadata=message_dict.get("metadata")
                    )
                elif self.messageType == "DeviceTelemetry":
                    return ProtobufEnvelope.create_telemetry_envelope(
                        device_id=message_dict["deviceId"],
                        seq=message_dict["seq"],
                        ts=message_dict["ts"],
                        sensors=message_dict.get("sensors", []),
                        system_info=message_dict.get("system")
                    )
                else:
                    # Fallback to JSON for unknown message types
                    return fastjson.dumps(asdict(self)).encode("utf-8")

            except (ProtobufEnvelopeError, KeyError, json.JSONDecodeError):
                # Fallback to JSON on any protobuf error
                pass

        # JSON serialization (default/fallback)
        return fastjson.dumps(asdict(self)).encode("utf-8")

    @staticmethod
    def FromDict(d: Dict[str, Any]) -> "GenericMessageEnvelope":
        """Create envelope from dictionary."""
        return GenericMessageEnvelope(
            messageType=d.get("messageType", "DeviceStatus"),
            messageVersion=d.get("messageVersion", "1.0"),
            message=d.get("message", "{}"),
            sentAt=d.get("sentAt", int(time.time())),
        )

    @staticmethod
    def FromProtobuf(data: bytes) -> Optional["GenericMessageEnvelope"]:
        """
        Create envelope from protobuf data.

        Args:
            data: Serialized protobuf envelope

        Returns:
            GenericMessageEnvelope instance or None if parsing fails
        """
        if not ProtobufEnvelope.is_available():
            return None

        try:
            parsed = ProtobufEnvelope.parse_envelope(data)
            return GenericMessageEnvelope(
                messageType=parsed["messageType"],
                messageVersion=parsed["messageVersion"],
                message=json.dumps(parsed["message"]),  # Convert to JSON string for compatibility
                sentAt=parsed["sentAt"]
            )
        except ProtobufEnvelopeError:
            return None

    @staticmethod
    def is_protobuf_enabled() -> bool:
        """Check if protobuf mode is enabled and available."""
        return USE_PROTOBUF and ProtobufEnvelope.is_available()
