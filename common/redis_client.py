from __future__ import annotations
import os
import json
import time
from typing import Dict, Any, Optional, List
import redis


class RedisClient:
    """Redis client wrapper for MQTT device message storage."""
    
    def __init__(self):
        self.client: Optional[redis.Redis] = None
        self.connected = False
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize Redis connection with configuration from environment."""
        try:
            host = os.getenv("REDIS_HOST", "localhost")
            port = int(os.getenv("REDIS_PORT", "6379"))
            db = int(os.getenv("REDIS_DB", "0"))
            password = os.getenv("REDIS_PASSWORD", None)
            
            self.client = redis.Redis(
                host=host,
                port=port,
                db=db,
                password=password,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # Test connection
            self.client.ping()
            self.connected = True
            print(f"[redis] Connected to {host}:{port}")
            
        except Exception as e:
            print(f"[redis] Connection failed: {e}")
            self.client = None
            self.connected = False
    
    def is_connected(self) -> bool:
        """Check if Redis client is connected."""
        return self.connected and self.client is not None
    
    def save_device_message(self, payload: Dict[str, Any], ttl_seconds: int = 3600) -> bool:
        """
        Save a device message to Redis with indexing.
        
        Args:
            payload: The validated device message payload
            ttl_seconds: Time to live for the message (default: 1 hour)
            
        Returns:
            bool: True if saved successfully, False otherwise
        """
        if not self.is_connected():
            return False
        
        try:
            device_id = payload['deviceId']
            seq = payload['seq']
            timestamp = payload['ts']
            
            # Create unique key for the message
            message_key = f"device:{device_id}:{seq}:{timestamp}"
            
            # Save the complete payload as JSON with TTL
            self.client.setex(
                message_key,
                ttl_seconds,
                json.dumps(payload)
            )
            
            # Maintain a sorted set for each device for easy querying
            device_index_key = f"device_messages:{device_id}"
            self.client.zadd(device_index_key, {message_key: timestamp})
            self.client.expire(device_index_key, ttl_seconds)
            
            return True
            
        except Exception as e:
            print(f"[redis] Save error: {e}")
            return False
    
    def get_device_messages(self, device_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent messages for a specific device.
        
        Args:
            device_id: The device identifier
            limit: Maximum number of messages to retrieve
            
        Returns:
            List of message dictionaries
        """
        if not self.is_connected():
            return []
        
        try:
            device_index_key = f"device_messages:{device_id}"
            
            # Get message keys sorted by timestamp (most recent first)
            message_keys = self.client.zrevrange(device_index_key, 0, limit - 1)
            
            messages = []
            for key in message_keys:
                message_data = self.client.get(key)
                if message_data:
                    messages.append(json.loads(message_data))
            
            return messages
            
        except Exception as e:
            print(f"[redis] Get messages error: {e}")
            return []
    
    def get_all_devices(self) -> List[str]:
        """
        Get list of all devices that have messages stored.
        
        Returns:
            List of device IDs
        """
        if not self.is_connected():
            return []
        
        try:
            device_keys = self.client.keys("device_messages:*")
            devices = [key.replace("device_messages:", "") for key in device_keys]
            return sorted(devices)
            
        except Exception as e:
            print(f"[redis] Get devices error: {e}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get Redis storage statistics.
        
        Returns:
            Dictionary with statistics
        """
        if not self.is_connected():
            return {"connected": False}
        
        try:
            device_keys = self.client.keys("device_messages:*")
            total_devices = len(device_keys)
            
            total_messages = 0
            for device_key in device_keys:
                total_messages += self.client.zcard(device_key)
            
            memory_info = self.client.info("memory")
            server_info = self.client.info("server")
            
            return {
                "connected": True,
                "total_devices": total_devices,
                "total_messages": total_messages,
                "memory_used_mb": round(memory_info.get("used_memory", 0) / 1024 / 1024, 2),
                "redis_version": server_info.get("redis_version", "unknown")
            }
            
        except Exception as e:
            print(f"[redis] Stats error: {e}")
            return {"connected": False, "error": str(e)}
    
    def cleanup_expired_devices(self, max_age_seconds: int = 7200) -> int:
        """
        Clean up device indexes that haven't been updated recently.
        
        Args:
            max_age_seconds: Maximum age for device indexes (default: 2 hours)
            
        Returns:
            Number of cleaned up device indexes
        """
        if not self.is_connected():
            return 0
        
        try:
            current_time = int(time.time() * 1000)  # milliseconds
            cutoff_time = current_time - (max_age_seconds * 1000)
            
            device_keys = self.client.keys("device_messages:*")
            cleaned_count = 0
            
            for device_key in device_keys:
                # Remove old messages from the sorted set
                removed = self.client.zremrangebyscore(device_key, 0, cutoff_time)
                if removed > 0:
                    cleaned_count += 1
                
                # If the sorted set is empty, remove it entirely
                if self.client.zcard(device_key) == 0:
                    self.client.delete(device_key)
            
            return cleaned_count
            
        except Exception as e:
            print(f"[redis] Cleanup error: {e}")
            return 0
