from __future__ import annotations
import time
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass

try:
    from proto_gen.device_messages_pb2 import (
        GenericMessageEnvelope,
        DeviceStatusMessage,
        DeviceStatus,
        Location,
        BatteryInfo,
        DeviceTelemetryMessage,
        SensorReading,
        SensorType,
        SystemInfo,
        DeviceCommandMessage,
        CommandType
    )
    PROTOBUF_AVAILABLE = True
except ImportError:
    PROTOBUF_AVAILABLE = False
    print("[protobuf] Protobuf modules not available. Run 'python build_protos.py' to generate them.")


class ProtobufEnvelopeError(Exception):
    """Exception raised for protobuf envelope errors."""
    pass


class ProtobufEnvelope:
    """Protobuf-based message envelope for device communications."""
    
    @staticmethod
    def is_available() -> bool:
        """Check if protobuf modules are available."""
        return PROTOBUF_AVAILABLE
    
    @staticmethod
    def create_device_status_envelope(
        device_id: str,
        status: str,
        seq: int,
        ts: int,
        location: Optional[Dict[str, Any]] = None,
        battery: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> bytes:
        """
        Create a device status message envelope.
        
        Args:
            device_id: Device identifier
            status: Device status (READY, IN_TRANSIT, STUCK, etc.)
            seq: Sequence number
            ts: Timestamp in milliseconds
            location: Optional location data
            battery: Optional battery data
            metadata: Optional metadata dictionary
            
        Returns:
            Serialized protobuf message
        """
        if not PROTOBUF_AVAILABLE:
            raise ProtobufEnvelopeError("Protobuf modules not available")
        
        # Create device status message
        device_msg = DeviceStatusMessage()
        device_msg.device_id = device_id
        device_msg.seq = seq
        device_msg.ts = ts
        
        # Set status
        status_map = {
            "READY": DeviceStatus.DEVICE_STATUS_READY,
            "IN_TRANSIT": DeviceStatus.DEVICE_STATUS_IN_TRANSIT,
            "STUCK": DeviceStatus.DEVICE_STATUS_STUCK,
            "OFFLINE": DeviceStatus.DEVICE_STATUS_OFFLINE,
            "MAINTENANCE": DeviceStatus.DEVICE_STATUS_MAINTENANCE,
        }
        device_msg.status = status_map.get(status, DeviceStatus.DEVICE_STATUS_UNKNOWN)
        
        # Add location if provided
        if location:
            loc = Location()
            loc.latitude = location.get("latitude", 0.0)
            loc.longitude = location.get("longitude", 0.0)
            if "altitude" in location:
                loc.altitude = location["altitude"]
            if "accuracy" in location:
                loc.accuracy = location["accuracy"]
            loc.timestamp = location.get("timestamp", ts)
            device_msg.location.CopyFrom(loc)
        
        # Add battery info if provided
        if battery:
            bat = BatteryInfo()
            bat.level = battery.get("level", 0.0)
            bat.charging = battery.get("charging", False)
            if "voltage" in battery:
                bat.voltage = battery["voltage"]
            if "temperature" in battery:
                bat.temperature = battery["temperature"]
            device_msg.battery.CopyFrom(bat)
        
        # Add metadata if provided
        if metadata:
            for key, value in metadata.items():
                device_msg.metadata[key] = value
        
        # Create envelope
        envelope = GenericMessageEnvelope()
        envelope.message_type = "DeviceStatus"
        envelope.message_version = "1.0"
        envelope.message = device_msg.SerializeToString()
        envelope.sent_at = int(time.time())
        
        return envelope.SerializeToString()
    
    @staticmethod
    def create_telemetry_envelope(
        device_id: str,
        seq: int,
        ts: int,
        sensors: list[Dict[str, Any]],
        system_info: Optional[Dict[str, Any]] = None
    ) -> bytes:
        """
        Create a device telemetry message envelope.
        
        Args:
            device_id: Device identifier
            seq: Sequence number
            ts: Timestamp in milliseconds
            sensors: List of sensor readings
            system_info: Optional system information
            
        Returns:
            Serialized protobuf message
        """
        if not PROTOBUF_AVAILABLE:
            raise ProtobufEnvelopeError("Protobuf modules not available")
        
        # Create telemetry message
        telemetry_msg = DeviceTelemetryMessage()
        telemetry_msg.device_id = device_id
        telemetry_msg.seq = seq
        telemetry_msg.ts = ts
        
        # Add sensor readings
        sensor_type_map = {
            "temperature": SensorType.SENSOR_TYPE_TEMPERATURE,
            "humidity": SensorType.SENSOR_TYPE_HUMIDITY,
            "pressure": SensorType.SENSOR_TYPE_PRESSURE,
            "accelerometer": SensorType.SENSOR_TYPE_ACCELEROMETER,
            "gyroscope": SensorType.SENSOR_TYPE_GYROSCOPE,
            "gps": SensorType.SENSOR_TYPE_GPS,
            "proximity": SensorType.SENSOR_TYPE_PROXIMITY,
            "light": SensorType.SENSOR_TYPE_LIGHT,
        }
        
        for sensor_data in sensors:
            sensor = SensorReading()
            sensor.sensor_id = sensor_data.get("sensor_id", "")
            sensor.type = sensor_type_map.get(
                sensor_data.get("type", "").lower(), 
                SensorType.SENSOR_TYPE_UNKNOWN
            )
            sensor.value = sensor_data.get("value", 0.0)
            sensor.unit = sensor_data.get("unit", "")
            sensor.timestamp = sensor_data.get("timestamp", ts)
            telemetry_msg.sensors.append(sensor)
        
        # Add system info if provided
        if system_info:
            sys_info = SystemInfo()
            sys_info.cpu_usage = system_info.get("cpu_usage", 0.0)
            sys_info.memory_usage = system_info.get("memory_usage", 0.0)
            sys_info.disk_usage = system_info.get("disk_usage", 0.0)
            sys_info.network_usage = system_info.get("network_usage", 0.0)
            sys_info.uptime = system_info.get("uptime", 0)
            telemetry_msg.system.CopyFrom(sys_info)
        
        # Create envelope
        envelope = GenericMessageEnvelope()
        envelope.message_type = "DeviceTelemetry"
        envelope.message_version = "1.0"
        envelope.message = telemetry_msg.SerializeToString()
        envelope.sent_at = int(time.time())
        
        return envelope.SerializeToString()
    
    @staticmethod
    def parse_envelope(data: bytes) -> Dict[str, Any]:
        """
        Parse a protobuf envelope and extract the inner message.
        
        Args:
            data: Serialized protobuf envelope
            
        Returns:
            Dictionary containing parsed message data
        """
        if not PROTOBUF_AVAILABLE:
            raise ProtobufEnvelopeError("Protobuf modules not available")
        
        try:
            # Parse envelope
            envelope = GenericMessageEnvelope()
            envelope.ParseFromString(data)
            
            result = {
                "messageType": envelope.message_type,
                "messageVersion": envelope.message_version,
                "sentAt": envelope.sent_at,
            }
            
            # Parse inner message based on type
            if envelope.message_type == "DeviceStatus":
                device_msg = DeviceStatusMessage()
                device_msg.ParseFromString(envelope.message)
                
                # Convert to dictionary format compatible with existing validation
                result["message"] = {
                    "deviceId": device_msg.device_id,
                    "status": DeviceStatus.Name(device_msg.status).replace("DEVICE_STATUS_", ""),
                    "seq": device_msg.seq,
                    "ts": device_msg.ts,
                }
                
                # Add optional fields if present
                if device_msg.HasField("location"):
                    result["message"]["location"] = {
                        "latitude": device_msg.location.latitude,
                        "longitude": device_msg.location.longitude,
                        "timestamp": device_msg.location.timestamp,
                    }
                    if device_msg.location.HasField("altitude"):
                        result["message"]["location"]["altitude"] = device_msg.location.altitude
                    if device_msg.location.HasField("accuracy"):
                        result["message"]["location"]["accuracy"] = device_msg.location.accuracy
                
                if device_msg.HasField("battery"):
                    result["message"]["battery"] = {
                        "level": device_msg.battery.level,
                        "charging": device_msg.battery.charging,
                    }
                    if device_msg.battery.HasField("voltage"):
                        result["message"]["battery"]["voltage"] = device_msg.battery.voltage
                    if device_msg.battery.HasField("temperature"):
                        result["message"]["battery"]["temperature"] = device_msg.battery.temperature
                
                if device_msg.metadata:
                    result["message"]["metadata"] = dict(device_msg.metadata)
            
            elif envelope.message_type == "DeviceTelemetry":
                telemetry_msg = DeviceTelemetryMessage()
                telemetry_msg.ParseFromString(envelope.message)
                
                result["message"] = {
                    "deviceId": telemetry_msg.device_id,
                    "seq": telemetry_msg.seq,
                    "ts": telemetry_msg.ts,
                    "sensors": []
                }
                
                # Add sensor readings
                for sensor in telemetry_msg.sensors:
                    sensor_data = {
                        "sensor_id": sensor.sensor_id,
                        "type": SensorType.Name(sensor.type).replace("SENSOR_TYPE_", "").lower(),
                        "value": sensor.value,
                        "unit": sensor.unit,
                        "timestamp": sensor.timestamp,
                    }
                    result["message"]["sensors"].append(sensor_data)
                
                # Add system info if present
                if telemetry_msg.HasField("system"):
                    result["message"]["system"] = {
                        "cpu_usage": telemetry_msg.system.cpu_usage,
                        "memory_usage": telemetry_msg.system.memory_usage,
                        "disk_usage": telemetry_msg.system.disk_usage,
                        "network_usage": telemetry_msg.system.network_usage,
                        "uptime": telemetry_msg.system.uptime,
                    }
            
            else:
                # Unknown message type, return raw bytes
                result["message"] = envelope.message
            
            return result
            
        except Exception as e:
            raise ProtobufEnvelopeError(f"Failed to parse protobuf envelope: {e}")
