from __future__ import annotations
import json
from typing import Any, Dict

class DiscardMessage(Exception):
    """Raise to drop the current message quietly."""
    pass

REQUIRED_FIELDS = {"deviceId", "status", "seq", "ts"}

# quick sanity validator — adapt to your schema business rules

def validate_payload_dict(d: Dict[str, Any]) -> Dict[str, Any]:
    missing = REQUIRED_FIELDS - set(d)
    if missing:
        raise DiscardMessage(f"missing fields: {sorted(missing)}")

    if not isinstance(d["deviceId"], (str, int)):
        raise DiscardMessage("deviceId must be str|int")
    if d.get("status") not in {"READY", "IN_TRANSIT", "STUCK"}:
        raise DiscardMessage("invalid status")

    return d
