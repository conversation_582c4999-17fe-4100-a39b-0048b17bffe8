FROM python:3.11-slim

WORKDIR /app

# Install only the dependencies needed for the simulator
COPY requirements.simulator.txt /app/.
RUN pip install --no-cache-dir -r requirements.simulator.txt

# Copy only the necessary files for the simulator
COPY common/ ./common/
COPY examples/ ./examples/
COPY simulator/ ./simulator/
COPY protos/ ./protos/
COPY build_protos.py ./

# Build protobuf files
RUN python build_protos.py

# Set environment variables for the simulator
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Default command to run the simulator
CMD ["python", "-m", "simulator.simulator", "--broker", "mosquitto", "--port", "1883", "--devices", "100", "--rate", "2.0", "--invalid-rate", "0.01"]
