#!/usr/bin/env python3
"""
Example demonstrating protobuf message creation and parsing.

This script shows how to create and parse different types of protobuf messages
used in the MQTT device simulator system.
"""

import time
from common.protobuf_envelope import ProtobufEnvelope, ProtobufEnvelopeError
from common.envelope import GenericMessageEnvelope


def example_device_status():
    """Example of creating a device status message."""
    print("=== Device Status Message Example ===")
    
    if not ProtobufEnvelope.is_available():
        print("Protobuf modules not available. Run 'make build-protos' first.")
        return
    
    try:
        # Create a device status message with all optional fields
        message_data = ProtobufEnvelope.create_device_status_envelope(
            device_id="dev-example-001",
            status="IN_TRANSIT",
            seq=42,
            ts=int(time.time() * 1000),
            location={
                "latitude": 37.7749,
                "longitude": -122.4194,
                "altitude": 100.5,
                "accuracy": 5.0,
                "timestamp": int(time.time() * 1000)
            },
            battery={
                "level": 0.85,
                "charging": False,
                "voltage": 3.7,
                "temperature": 25.5
            },
            metadata={
                "firmware_version": "1.2.3",
                "hardware_revision": "rev-b",
                "operator": "test-user"
            }
        )
        
        print(f"Created protobuf message ({len(message_data)} bytes)")
        
        # Parse the message back
        parsed = ProtobufEnvelope.parse_envelope(message_data)
        print("Parsed message:")
        print(f"  Type: {parsed['messageType']}")
        print(f"  Version: {parsed['messageVersion']}")
        print(f"  Device ID: {parsed['message']['deviceId']}")
        print(f"  Status: {parsed['message']['status']}")
        print(f"  Sequence: {parsed['message']['seq']}")
        print(f"  Location: {parsed['message'].get('location', 'N/A')}")
        print(f"  Battery: {parsed['message'].get('battery', 'N/A')}")
        print(f"  Metadata: {parsed['message'].get('metadata', 'N/A')}")
        
    except ProtobufEnvelopeError as e:
        print(f"Protobuf error: {e}")


def example_telemetry():
    """Example of creating a telemetry message."""
    print("\n=== Device Telemetry Message Example ===")
    
    if not ProtobufEnvelope.is_available():
        print("Protobuf modules not available. Run 'make build-protos' first.")
        return
    
    try:
        # Create a telemetry message with sensor readings
        message_data = ProtobufEnvelope.create_telemetry_envelope(
            device_id="dev-example-002",
            seq=123,
            ts=int(time.time() * 1000),
            sensors=[
                {
                    "sensor_id": "temp-01",
                    "type": "temperature",
                    "value": 23.5,
                    "unit": "celsius",
                    "timestamp": int(time.time() * 1000)
                },
                {
                    "sensor_id": "humid-01",
                    "type": "humidity",
                    "value": 65.2,
                    "unit": "percent",
                    "timestamp": int(time.time() * 1000)
                },
                {
                    "sensor_id": "accel-01",
                    "type": "accelerometer",
                    "value": 9.81,
                    "unit": "m/s²",
                    "timestamp": int(time.time() * 1000)
                }
            ],
            system_info={
                "cpu_usage": 45.2,
                "memory_usage": 67.8,
                "disk_usage": 23.1,
                "network_usage": 12.5,
                "uptime": 86400  # 1 day in seconds
            }
        )
        
        print(f"Created telemetry message ({len(message_data)} bytes)")
        
        # Parse the message back
        parsed = ProtobufEnvelope.parse_envelope(message_data)
        print("Parsed telemetry:")
        print(f"  Type: {parsed['messageType']}")
        print(f"  Device ID: {parsed['message']['deviceId']}")
        print(f"  Sensors: {len(parsed['message']['sensors'])} readings")
        for i, sensor in enumerate(parsed['message']['sensors']):
            print(f"    {i+1}. {sensor['sensor_id']}: {sensor['value']} {sensor['unit']} ({sensor['type']})")
        
        if 'system' in parsed['message']:
            sys_info = parsed['message']['system']
            print(f"  System Info:")
            print(f"    CPU: {sys_info['cpu_usage']}%")
            print(f"    Memory: {sys_info['memory_usage']}%")
            print(f"    Disk: {sys_info['disk_usage']}%")
            print(f"    Uptime: {sys_info['uptime']}s")
        
    except ProtobufEnvelopeError as e:
        print(f"Protobuf error: {e}")


def example_envelope_compatibility():
    """Example showing envelope compatibility between JSON and protobuf."""
    print("\n=== Envelope Compatibility Example ===")
    
    # Create a message using the GenericMessageEnvelope
    envelope = GenericMessageEnvelope(
        messageType="DeviceStatus",
        messageVersion="1.0",
        message={
            "deviceId": "dev-compat-test",
            "status": "READY",
            "seq": 1,
            "ts": int(time.time() * 1000)
        },
        sentAt=int(time.time())
    )
    
    # Serialize (will use protobuf if available, JSON otherwise)
    serialized = envelope.SerializeToString()
    
    if GenericMessageEnvelope.is_protobuf_enabled():
        print(f"Using protobuf format ({len(serialized)} bytes)")
        
        # Try to parse as protobuf
        parsed_envelope = GenericMessageEnvelope.FromProtobuf(serialized)
        if parsed_envelope:
            print("Successfully parsed protobuf envelope")
            print(f"  Message type: {parsed_envelope.messageType}")
            print(f"  Device ID: {parsed_envelope.message}")
        else:
            print("Failed to parse as protobuf")
    else:
        print(f"Using JSON format ({len(serialized)} bytes)")
        print("Protobuf not available or disabled")


def main():
    """Run all examples."""
    print("Protobuf Message Examples")
    print("=" * 50)
    
    example_device_status()
    example_telemetry()
    example_envelope_compatibility()
    
    print("\n" + "=" * 50)
    print("Examples completed!")


if __name__ == "__main__":
    main()
