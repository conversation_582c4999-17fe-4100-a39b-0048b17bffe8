#!/usr/bin/env python3
"""
Redis Query Utility for MQTT Device Messages

This script provides utilities to query and analyze device messages stored in Redis.
"""

import argparse
from typing import List, Dict, Any
from common.redis_client import RedisClient

def main():
    parser = argparse.ArgumentParser(description="Query MQTT device messages from Redis")
    parser.add_argument("--list-devices", action="store_true", help="List all devices")
    parser.add_argument("--device", type=str, help="Get messages for specific device")
    parser.add_argument("--limit", type=int, default=10, help="Limit number of messages (default: 10)")
    parser.add_argument("--stats", action="store_true", help="Show overall statistics")
    parser.add_argument("--cleanup", action="store_true", help="Clean up expired device indexes")

    args = parser.parse_args()

    redis_client = RedisClient()
    if not redis_client.is_connected():
        print("Failed to connect to Redis")
        return 1

    if args.list_devices:
        devices = redis_client.get_all_devices()
        print(f"Found {len(devices)} devices:")
        for device in devices:
            print(f"  - {device}")

    elif args.device:
        messages = redis_client.get_device_messages(args.device, args.limit)
        print(f"Recent {len(messages)} messages for device '{args.device}':")
        for i, msg in enumerate(messages, 1):
            print(f"  {i}. seq={msg.get('seq')} status={msg.get('status')} ts={msg.get('ts')}")
            if len(str(msg)) < 200:  # Show full message if short
                print(f"     {msg}")

    elif args.stats:
        stats = redis_client.get_stats()
        if stats.get("connected"):
            print("Redis Statistics:")
            print(f"  Total devices: {stats['total_devices']}")
            print(f"  Total messages: {stats['total_messages']}")
            print(f"  Memory used: {stats['memory_used_mb']} MB")
            print(f"  Redis version: {stats['redis_version']}")
        else:
            print(f"Redis connection error: {stats.get('error', 'Unknown error')}")

    elif args.cleanup:
        cleaned = redis_client.cleanup_expired_devices()
        print(f"Cleaned up {cleaned} expired device indexes")

    else:
        parser.print_help()

    return 0

if __name__ == "__main__":
    exit(main())
