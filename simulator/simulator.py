from __future__ import annotations
import argparse, random, string, threading, time
from typing import Dict
import paho.mqtt.client as mqtt
import ujson as json

from common.envelope import GenericMessageEnvelope
from examples import json_sample

STAT = {
    "sent": 0,
    "invalid": 0,
    "errors": 0,
}

STAT_LOCK = threading.Lock()

STAT_EVERY = 5  # seconds

STAT_RUN = True

def _rand_id(n=6):
    return "dev-" + "".join(random.choices(string.ascii_lowercase + string.digits, k=n))

STATUSES = ["READY", "IN_TRANSIT", "STUCK"]

class DeviceThread(threading.Thread):
    def __init__(self, idx: int, broker: str, port: int, topic_root: str, rate: float, timing: int, invalid_rate: float):
        super().__init__(daemon=True)
        self.idx = idx
        self.client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2, client_id=f"sim-{idx}")
        self.client.connect(broker, port, keepalive=30)
        self.topic_root = topic_root.rstrip("/")
        self.rate = rate
        self.timing = timing
        self.invalid_rate = invalid_rate
        self.device_id = _rand_id()
        self.seq = 0

        self.json_sample = json.dumps(json_sample)

    def run(self):
        self.client.loop_start()
        try:
            while STAT_RUN:
                self.seq += 1
                payload = {
                    "deviceId": self.device_id,
                    "status": random.choice(STATUSES),
                    "seq": self.seq,
                    "ts": int(time.time()*1000),
                }

                # self.json_sample.update({}) 

                # Inject invalid message occasionally
                if random.random() < self.invalid_rate:
                    payload.pop("status", None)

                # Create envelope (will use protobuf if enabled and available)
                env = GenericMessageEnvelope(
                    messageType="DeviceStatus",
                    messageVersion="1.0",
                    message=payload,  # Pass dict directly for protobuf compatibility
                    sentAt=int(time.time()),
                )

                try:
                    topic = f"{self.topic_root}/{self.device_id}"
                    self.client.publish(topic, env.SerializeToString(), qos=0)
                    with STAT_LOCK:
                        if "status" not in payload:
                            STAT["invalid"] += 1
                        else:
                            STAT["sent"] += 1
                except Exception:
                    with STAT_LOCK:
                        STAT["errors"] += 1

                time.sleep(max(0.0, 1.0 / self.rate))
        finally:
            self.client.loop_stop()
            self.client.disconnect()


def stat_loop():
    while STAT_RUN:
        time.sleep(STAT_EVERY)
        with STAT_LOCK:
            print(f"[sim] sent={STAT['sent']} invalid_injected={STAT['invalid']} errors={STAT['errors']}")


def main():
    p = argparse.ArgumentParser()
    p.add_argument("--broker", default="localhost")
    p.add_argument("--port", type=int, default=1883)
    p.add_argument("--devices", type=int, default=100)
    p.add_argument("--rate", type=float, default=2.0, help="msgs/sec per device")
    p.add_argument("--burst", type=int, default=1, help="not used; placeholder for future burst mode")
    p.add_argument("--topic-root", type=str, default="device/messages")
    p.add_argument("--invalid-rate", type=float, default=0.0, help="probability [0..1] to inject invalid msg")
    p.add_argument("--timing", type=int, default=60)
    args = p.parse_args()

    threads = [DeviceThread(i, args.broker, args.port, args.topic_root, args.rate, args.timing, args.invalid_rate)
               for i in range(args.devices)]

    for t in threads: t.start()

    tstat = threading.Thread(target=stat_loop, daemon=True)
    tstat.start()

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        pass
    finally:
        global STAT_RUN
        STAT_RUN = False
        for t in threads: t.join(timeout=2)

if __name__ == "__main__":
    main()
