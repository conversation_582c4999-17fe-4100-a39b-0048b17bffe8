version: "3.9"
services:
  mosquitto:
    image: eclipse-mosquitto:2
    ports: ["1883:1883", "9001:9001"]
    volumes:
      - ./mosquitto.conf:/mosquitto/config/mosquitto.conf:ro
    healthcheck:
      test: ["CMD", "mosquitto_pub", "-h", "localhost", "-t", "health", "-m", "ok"]
      interval: 10s
      timeout: 3s
      retries: 5

  redis:
    image: redis:7-alpine
    ports: ["6379:6379"]
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  consumer:
    build: .
    image: device-consumer:latest
    command: python -m consumer.consumer
    environment:
      - MQTT_HOST=mosquitto
      - MQTT_PORT=1883
      - MQTT_TOPIC=device/messages/#
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - USE_PROTOBUF=true
      - LOG_LEVEL=INFO
    depends_on:
      - mosquitto
      - redis
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  simulator:
    build:
      context: .
      dockerfile: Dockerfile.simulator
    image: device-simulator:latest
    environment:
      - MQTT_HOST=mosquitto
      - MQTT_PORT=1883
      - MQTT_TOPIC_ROOT=device/messages
      - USE_PROTOBUF=true
      - SIMULATOR_DEVICES=100
      - SIMULATOR_RATE=2.0
      - SIMULATOR_INVALID_RATE=0.01
    depends_on:
      - mosquitto
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    profiles:
      - simulator
    command: >
      python -m simulator.simulator
      --broker mosquitto
      --port 1883
      --devices ${SIMULATOR_DEVICES:-100}
      --rate ${SIMULATOR_RATE:-2.0}
      --invalid-rate ${SIMULATOR_INVALID_RATE:-0.01}
      --topic-root ${MQTT_TOPIC_ROOT:-device/messages}

volumes:
  redis_data:
