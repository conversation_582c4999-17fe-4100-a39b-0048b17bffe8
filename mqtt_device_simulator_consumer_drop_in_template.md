# MQTT Device Simulator + Consumer — Drop‑in Template

> Purpose: mirror your setup to (a) blast concurrent device messages, (b) track every message, (c) break on invalid payloads, and (d) produce simple metrics for perf checks without Prometheus.

---

## Project tree
```
.
├── docker-compose.yml
├── requirements.txt
├── Makefile
├── README.md
├── common/
│   ├── envelope.py
│   └── validators.py
├── simulator/
│   └── simulator.py
└── consumer/
    └── consumer.py
```

## requirements.txt
```
paho-mqtt==1.6.1
ujson==5.10.0
python-dotenv==1.0.1
```
> (If you truly need asyncio, swap simulator to `asyncio-mqtt`. For now, keeping it lean with paho-mqtt to avoid over‑engineering.)

## docker-compose.yml (Mosquitto + Consumer)
```yaml
version: "3.9"
services:
  mosquitto:
    image: eclipse-mosquitto:2
    ports: ["1883:1883", "9001:9001"]
    volumes:
      - ./mosquitto.conf:/mosquitto/config/mosquitto.conf:ro
    healthcheck:
      test: ["CMD", "mosquitto_pub", "-h", "localhost", "-t", "health", "-m", "ok"]
      interval: 10s
      timeout: 3s
      retries: 5

  consumer:
    build: .
    image: device-consumer:latest
    command: python -m consumer.consumer
    environment:
      - MQTT_HOST=mosquitto
      - MQTT_PORT=1883
      - MQTT_TOPIC=device/messages/#
      - LOG_LEVEL=INFO
    depends_on:
      - mosquitto
```

### mosquitto.conf (place at project root)
```
listener 1883 0.0.0.0
allow_anonymous true
persistence true
log_type all
```

## Makefile
```make
.PHONY: up down sim log-consumer stats

up:
	docker compose up -d --build

Down:
	docker compose down -v

sim:
	python -m simulator.simulator \
	  --broker localhost --port 1883 \
	  --devices 500 --rate 3 --burst 1 \
	  --invalid-rate 0.01

log-consumer:
	docker compose logs -f consumer

stats:
	docker stats --no-stream
```

---

## common/envelope.py
```python
from __future__ import annotations
import time, json, ujson as fastjson
from dataclasses import dataclass, asdict
from typing import Any, Dict

# If you use protobuf, replace this with your generated consumer_pb2.GenericMessageEnvelope
# and call ParseFromString in consumer.unpack_payload.

@dataclass
class GenericMessageEnvelope:
    messageType: str
    messageVersion: str
    message: str  # JSON string payload for simplicity here
    sentAt: int

    def SerializeToString(self) -> bytes:
        return fastjson.dumps(asdict(self)).encode("utf-8")

    @staticmethod
    def FromDict(d: Dict[str, Any]) -> "GenericMessageEnvelope":
        return GenericMessageEnvelope(
            messageType=d.get("messageType", "DeviceStatus"),
            messageVersion=d.get("messageVersion", "1.0"),
            message=d.get("message", "{}"),
            sentAt=d.get("sentAt", int(time.time())),
        )
```

## common/validators.py
```python
from __future__ import annotations
import json
from typing import Any, Dict

class DiscardMessage(Exception):
    """Raise to drop the current message quietly."""
    pass

REQUIRED_FIELDS = {"deviceId", "status", "seq", "ts"}

# quick sanity validator — adapt to your schema business rules

def validate_payload_dict(d: Dict[str, Any]) -> Dict[str, Any]:
    missing = REQUIRED_FIELDS - set(d)
    if missing:
        raise DiscardMessage(f"missing fields: {sorted(missing)}")

    if not isinstance(d["deviceId"], (str, int)):
        raise DiscardMessage("deviceId must be str|int")
    if d.get("status") not in {"READY", "IN_TRANSIT", "STUCK"}:
        raise DiscardMessage("invalid status")

    return d
```

---

## simulator/simulator.py (concurrent device message blaster)
```python
from __future__ import annotations
import argparse, random, string, threading, time
from typing import Dict
import paho.mqtt.client as mqtt
import ujson as json

from common.envelope import GenericMessageEnvelope

STAT = {
    "sent": 0,
    "invalid": 0,
    "errors": 0,
}

STAT_LOCK = threading.Lock()

STAT_EVERY = 5  # seconds

STAT_RUN = True

def _rand_id(n=6):
    return "dev-" + "".join(random.choices(string.ascii_lowercase + string.digits, k=n))

STATUSES = ["READY", "IN_TRANSIT", "STUCK"]

class DeviceThread(threading.Thread):
    def __init__(self, idx: int, broker: str, port: int, topic_root: str, rate: float, invalid_rate: float):
        super().__init__(daemon=True)
        self.idx = idx
        self.client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2, client_id=f"sim-{idx}")
        self.client.connect(broker, port, keepalive=30)
        self.topic_root = topic_root.rstrip("/")
        self.rate = rate
        self.invalid_rate = invalid_rate
        self.device_id = _rand_id()
        self.seq = 0

    def run(self):
        self.client.loop_start()
        try:
            while STAT_RUN:
                self.seq += 1
                payload = {
                    "deviceId": self.device_id,
                    "status": random.choice(STATUSES),
                    "seq": self.seq,
                    "ts": int(time.time()*1000),
                }

                # Inject invalid message occasionally
                if random.random() < self.invalid_rate:
                    payload.pop("status", None)

                env = GenericMessageEnvelope(
                    messageType="DeviceStatus",
                    messageVersion="1.0",
                    message=json.dumps(payload),
                    sentAt=int(time.time()),
                )

                try:
                    topic = f"{self.topic_root}/{self.device_id}"
                    self.client.publish(topic, env.SerializeToString(), qos=0)
                    with STAT_LOCK:
                        if "status" not in payload:
                            STAT["invalid"] += 1
                        else:
                            STAT["sent"] += 1
                except Exception:
                    with STAT_LOCK:
                        STAT["errors"] += 1

                time.sleep(max(0.0, 1.0 / self.rate))
        finally:
            self.client.loop_stop()
            self.client.disconnect()


def stat_loop():
    while STAT_RUN:
        time.sleep(STAT_EVERY)
        with STAT_LOCK:
            print(f"[sim] sent={STAT['sent']} invalid_injected={STAT['invalid']} errors={STAT['errors']}")


def main():
    p = argparse.ArgumentParser()
    p.add_argument("--broker", default="localhost")
    p.add_argument("--port", type=int, default=1883)
    p.add_argument("--devices", type=int, default=100)
    p.add_argument("--rate", type=float, default=2.0, help="msgs/sec per device")
    p.add_argument("--burst", type=int, default=1, help="not used; placeholder for future burst mode")
    p.add_argument("--topic-root", default="device/messages")
    p.add_argument("--invalid-rate", type=float, default=0.0, help="probability [0..1] to inject invalid msg")
    args = p.parse_args()

    threads = [DeviceThread(i, args.broker, args.port, args.topic_root, args.rate, args.invalid_rate)
               for i in range(args.devices)]

    for t in threads: t.start()

    tstat = threading.Thread(target=stat_loop, daemon=True)
    tstat.start()

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        pass
    finally:
        global STAT_RUN
        STAT_RUN = False
        for t in threads: t.join(timeout=2)

if __name__ == "__main__":
    main()
```

---

## consumer/consumer.py (track everything, break on invalid)
```python
from __future__ import annotations
import os, time, json as stdjson
from typing import Tuple, Dict, Any
import paho.mqtt.client as mqtt
import ujson as json

from common.validators import validate_payload_dict, DiscardMessage

MQTT_HOST = os.getenv("MQTT_HOST", "localhost")
MQTT_PORT = int(os.getenv("MQTT_PORT", "1883"))
MQTT_TOPIC = os.getenv("MQTT_TOPIC", "device/messages/#")

COUNTERS = {"received": 0, "ok": 0, "discarded": 0, "errors": 0}
REPORT_EVERY = int(os.getenv("REPORT_EVERY", "5"))

# If using real protobuf, swap this for ParseFromString

def unpack_payload(payload: bytes) -> Tuple[str, str, Dict[str, Any]]:
    try:
        d = json.loads(payload)
        messageType = d.get("messageType", "")
        messageVersion = d.get("messageVersion", "")
        message_str = d.get("message", "{}")
        payload_dict = stdjson.loads(message_str)
        return messageType, messageVersion, payload_dict
    except Exception as e:
        raise DiscardMessage(f"bad envelope: {e}")


def on_connect(client, userdata, flags, reason_code, properties):
    print(f"[consumer] connected rc={reason_code}")
    client.subscribe(MQTT_TOPIC, qos=0)


_last_report = time.time()

def on_message(client, userdata, msg):
    global _last_report
    COUNTERS["received"] += 1

    try:
        message_type, message_version, payload_dict = unpack_payload(msg.payload)
        # schema/business validation
        payload_checked = validate_payload_dict(payload_dict)

        # process the OK message
        # (replace with your downstream processing, queueing, DB, etc.)
        # Example: just echo minimal info
        # print(f"OK device={payload_checked['deviceId']} seq={payload_checked['seq']}")

        COUNTERS["ok"] += 1

    except DiscardMessage as e:
        COUNTERS["discarded"] += 1
        # Optionally log e quietly, or route to DLQ/topic
        # print(f"discarded: {e}")
    except Exception as e:
        COUNTERS["errors"] += 1
        # print(f"error: {e}")

    # lightweight periodic report
    if time.time() - _last_report >= REPORT_EVERY:
        print(
            f"[consumer] received={COUNTERS['received']} ok={COUNTERS['ok']} "
            f"discarded={COUNTERS['discarded']} errors={COUNTERS['errors']}"
        )
        _last_report = time.time()


def main():
    client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2, client_id="consumer-1")
    client.on_connect = on_connect
    client.on_message = on_message

    client.connect(MQTT_HOST, MQTT_PORT, keepalive=60)
    client.loop_forever(retry_first_connection=True)

if __name__ == "__main__":
    main()
```

---

## README.md (usage)
```md
# Quickstart

1) `pip install -r requirements.txt`
2) `docker compose up -d`  (spins mosquitto + consumer)
3) In another terminal run the simulator:

```bash
python -m simulator.simulator --broker localhost --port 1883 \
  --devices 500 --rate 3 --invalid-rate 0.01
```

You’ll see both **sim** and **consumer** print counters every ~5s.

## Notes
- To switch to real protobuf, replace `common/envelope.py` with your generated `consumer_pb2` and move the `ParseFromString(payload)` into `unpack_payload()`.
- The `DiscardMessage` exception is intentional so your handler can drop invalid messages without noisy tracebacks.
- For persistent metrics without Prometheus, redirect stdout to a file and parse later, or wrap counters with a tiny HTTP endpoint exposing JSON for your external poller.
```

---

## Why this mirrors your needs
- **Concurrent device sends** with lightweight threads & loop_start (paho standard)
- **Full tracking** of sent/invalid/errors on simulator; received/ok/discarded/errors on consumer
- **Break on invalid** by raising `DiscardMessage` right after unpack/validate (post‑`ParseFromString` in your real code)
- **Zero infra add‑ons** needed to start (Prometheus optional)

If you paste your actual files, we’ll align this template to your codebase 1:1 and review perf hotspots (QoS, batching, payload size, backpressure, parser cost, GC pressure, etc.).

