# Quickstart

1) `pip install -r requirements.txt`
2) `make build-protos`  (compile protobuf definitions)
3) `docker compose up -d`  (spins mosquitto + redis + consumer)
4) In another terminal run the simulator:

```bash
python -m simulator.simulator --broker localhost --port 1883 \
  --devices 500 --rate 3 --invalid-rate 0.01
```

You'll see both **sim** and **consumer** print counters every ~5s.

## Protobuf Support

The system now supports both JSON and Protocol Buffers (protobuf) message formats:

### Features
- **Dual Format Support**: Automatically detects and handles both JSON and protobuf messages
- **Rich Message Types**: Comprehensive protobuf definitions for device status, telemetry, and commands
- **Backward Compatibility**: Existing JSON-based code continues to work
- **Performance**: Protobuf offers better performance and smaller message sizes

### Message Types
- **DeviceStatusMessage**: Device status with location, battery, and metadata
- **DeviceTelemetryMessage**: Sensor readings and system information
- **DeviceCommandMessage**: Commands for device control

### Building Protobuf Files

```bash
# Compile protobuf definitions
make build-protos

# Clean generated files
make clean-protos
```

### Configuration

Set `USE_PROTOBUF=true` (default) to enable protobuf mode, or `USE_PROTOBUF=false` for JSON-only mode.

## Redis Integration

The consumer now automatically saves all validated device messages to Redis using a dedicated `RedisClient` class with the following features:

- **Message Storage**: Each message is stored with a unique key pattern: `device:{deviceId}:{seq}:{timestamp}`
- **Device Indexing**: Messages are indexed by device in sorted sets for easy querying
- **TTL**: Messages expire after 1 hour (configurable via `REDIS_TTL` env var)
- **Error Handling**: Redis failures don't stop message processing
- **Modular Design**: Redis functionality is separated into `common/redis_client.py`

### Querying Redis Data

Use the included `redis_query.py` utility:

```bash
# List all devices with stored messages
python redis_query.py --list-devices

# Get recent messages for a specific device
python redis_query.py --device dev-abc123 --limit 20

# Show overall statistics
python redis_query.py --stats

# Clean up expired device indexes
python redis_query.py --cleanup
```

### Makefile Commands

Additional Redis-related commands:

```bash
# Access Redis CLI directly
make redis-cli

# Show Redis storage statistics
make redis-stats

# Clean up expired data
make redis-cleanup
```

### Redis Configuration

Environment variables for Redis connection:
- `REDIS_HOST` (default: localhost)
- `REDIS_PORT` (default: 6379)
- `REDIS_DB` (default: 0)
- `REDIS_PASSWORD` (optional)
- `REDIS_TTL` (default: 3600 seconds)

## Notes
- **Protobuf Integration**: The system automatically uses protobuf when available and enabled. Protobuf definitions are in `protos/` and generated files go to `proto_gen/`.
- **Format Detection**: The consumer automatically detects and handles both JSON and protobuf message formats.
- **Error Handling**: The `DiscardMessage` exception allows graceful handling of invalid messages without noisy tracebacks.
- **Redis Integration**: Optional Redis storage continues working even if Redis is unavailable.
- **Metrics**: For persistent metrics without Prometheus, redirect stdout to a file and parse later, or wrap counters with a tiny HTTP endpoint exposing JSON for your external poller.
- **Dependencies**: Requires `protoc` (Protocol Compiler) to be installed for building protobuf files.
