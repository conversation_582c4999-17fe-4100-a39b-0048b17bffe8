# Quickstart

## Option 1: Containerized Simulator (Recommended)

1) `pip install -r requirements.txt`
2) `make build-protos`  (compile protobuf definitions)
3) `make up-with-sim`  (spins mosquitto + redis + consumer + simulator)

You'll see both **simulator** and **consumer** print counters every ~5s.

## Option 2: Local Simulator (Legacy)

1) `pip install -r requirements.txt`
2) `make build-protos`  (compile protobuf definitions)
3) `make up`  (spins mosquitto + redis + consumer)
4) In another terminal run the simulator:

```bash
python -m simulator.simulator --broker localhost --port 1883 \
  --devices 500 --rate 3 --invalid-rate 0.01
```

## Simulator Service Architecture

The simulator is now available as a separate Docker service, providing:

- **Resource Isolation**: Separate CPU and memory limits for simulator vs consumer
- **Independent Scaling**: Scale simulator and consumer services independently
- **Better Monitoring**: Isolated metrics and logging for each service
- **Flexible Configuration**: Environment-based configuration for different scenarios

## Protobuf Support

The system now supports both JSON and Protocol Buffers (protobuf) message formats:

### Features
- **Dual Format Support**: Automatically detects and handles both JSON and protobuf messages
- **Rich Message Types**: Comprehensive protobuf definitions for device status, telemetry, and commands
- **Backward Compatibility**: Existing JSON-based code continues to work
- **Performance**: Protobuf offers better performance and smaller message sizes

### Message Types
- **DeviceStatusMessage**: Device status with location, battery, and metadata
- **DeviceTelemetryMessage**: Sensor readings and system information
- **DeviceCommandMessage**: Commands for device control

### Building Protobuf Files

```bash
# Compile protobuf definitions
make build-protos

# Clean generated files
make clean-protos
```

### Configuration

Set `USE_PROTOBUF=true` (default) to enable protobuf mode, or `USE_PROTOBUF=false` for JSON-only mode.

### Simulator Configuration

The simulator service can be configured via environment variables:

```bash
# Number of simulated devices (default: 100)
SIMULATOR_DEVICES=500

# Messages per second per device (default: 2.0)
SIMULATOR_RATE=3.0

# Probability of invalid messages (default: 0.01)
SIMULATOR_INVALID_RATE=0.05

# MQTT topic root (default: device/messages)
MQTT_TOPIC_ROOT=device/messages
```

You can override these in your docker-compose.yml or create a `.env` file:

```bash
# .env file example
SIMULATOR_DEVICES=1000
SIMULATOR_RATE=5.0
SIMULATOR_INVALID_RATE=0.02
```

## Redis Integration

The consumer now automatically saves all validated device messages to Redis using a dedicated `RedisClient` class with the following features:

- **Message Storage**: Each message is stored with a unique key pattern: `device:{deviceId}:{seq}:{timestamp}`
- **Device Indexing**: Messages are indexed by device in sorted sets for easy querying
- **TTL**: Messages expire after 1 hour (configurable via `REDIS_TTL` env var)
- **Error Handling**: Redis failures don't stop message processing
- **Modular Design**: Redis functionality is separated into `common/redis_client.py`

### Querying Redis Data

Use the included `redis_query.py` utility:

```bash
# List all devices with stored messages
python redis_query.py --list-devices

# Get recent messages for a specific device
python redis_query.py --device dev-abc123 --limit 20

# Show overall statistics
python redis_query.py --stats

# Clean up expired device indexes
python redis_query.py --cleanup
```

### Service Management Commands

```bash
# Core services only (mosquitto + redis + consumer)
make up

# Core services + simulator
make up-with-sim

# Full stack with monitoring (includes Portainer dashboard)
make up-monitoring

# Stop all services
make down

# Stop all services including simulator and monitoring
make down-all

# Start only the simulator service
make sim-service

# Legacy: Run simulator locally
make sim
```

### Logging and Monitoring Commands

```bash
# View consumer logs
make log-consumer

# View simulator logs
make log-simulator

# View all service logs
make log-all

# Show Docker resource usage
make stats

# Comprehensive monitoring (stats + health + Redis info)
make monitor
```

### Redis Commands

```bash
# Access Redis CLI directly
make redis-cli

# Show Redis storage statistics
make redis-stats

# Clean up expired data
make redis-cleanup
```

### Redis Configuration

Environment variables for Redis connection:
- `REDIS_HOST` (default: localhost)
- `REDIS_PORT` (default: 6379)
- `REDIS_DB` (default: 0)
- `REDIS_PASSWORD` (optional)
- `REDIS_TTL` (default: 3600 seconds)

## Resource Monitoring and Isolation

### Service Resource Limits

The Docker Compose configuration includes resource limits for proper isolation:

**Consumer Service:**
- CPU Limit: 1.0 cores
- Memory Limit: 512MB
- CPU Reservation: 0.5 cores
- Memory Reservation: 256MB

**Simulator Service:**
- CPU Limit: 0.5 cores
- Memory Limit: 256MB
- CPU Reservation: 0.25 cores
- Memory Reservation: 128MB

### Monitoring Options

1. **Basic Monitoring**: Use `make stats` to see real-time resource usage
2. **Comprehensive Monitoring**: Use `make monitor` for detailed service health
3. **Advanced Monitoring**: Use `make up-monitoring` to include Portainer dashboard at http://localhost:9000

### Health Checks

Both services include health checks that verify MQTT connectivity:
- Check interval: 30 seconds
- Timeout: 10 seconds
- Retries: 3 attempts

## Notes
- **Protobuf Integration**: The system automatically uses protobuf when available and enabled. Protobuf definitions are in `protos/` and generated files go to `proto_gen/`.
- **Format Detection**: The consumer automatically detects and handles both JSON and protobuf message formats.
- **Error Handling**: The `DiscardMessage` exception allows graceful handling of invalid messages without noisy tracebacks.
- **Redis Integration**: Optional Redis storage continues working even if Redis is unavailable.
- **Metrics**: For persistent metrics without Prometheus, redirect stdout to a file and parse later, or wrap counters with a tiny HTTP endpoint exposing JSON for your external poller.
- **Dependencies**: Requires `protoc` (Protocol Compiler) to be installed for building protobuf files.
