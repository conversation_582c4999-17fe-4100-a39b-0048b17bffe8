version: "3.9"

# Override file for enhanced monitoring and resource tracking
# Usage: docker compose -f docker-compose.yml -f docker-compose.monitoring.yml up

services:
  consumer:
    labels:
      - "monitoring.service=consumer"
      - "monitoring.type=mqtt-consumer"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "python", "-c", "import paho.mqtt.client as mqtt; c = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2); c.connect('mosquitto', 1883, 5); c.disconnect()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  simulator:
    labels:
      - "monitoring.service=simulator"
      - "monitoring.type=mqtt-simulator"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "python", "-c", "import paho.mqtt.client as mqtt; c = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2); c.connect('mosquitto', 1883, 5); c.disconnect()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s

  # Optional: Add a monitoring dashboard (Portainer or similar)
  portainer:
    image: portainer/portainer-ce:latest
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  portainer_data:
