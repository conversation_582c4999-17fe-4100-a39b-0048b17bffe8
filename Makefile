.PHONY: up down up-with-sim up-monitoring down-all sim sim-service log-consumer log-simulator log-all stats monitor redis-cli redis-stats redis-cleanup build-protos clean-protos

build-protos:
	python build_protos.py

clean-protos:
	python build_protos.py clean

# Start core services (mosquitto, redis, consumer)
up: build-protos
	docker compose up -d --build

# Start core services + simulator service
up-with-sim: build-protos
	docker compose --profile simulator up -d --build

# Start with monitoring capabilities
up-monitoring: build-protos
	docker compose -f docker-compose.yml -f docker-compose.monitoring.yml --profile simulator --profile monitoring up -d --build

# Stop all services and remove volumes
down:
	docker compose down -v

# Stop all services including profiles
down-all:
	docker compose --profile simulator --profile monitoring down -v

# Run simulator locally (legacy mode)
sim:
	python -m simulator.simulator \
	  --broker localhost --port 1883 \
	  --devices 500 --rate 3 --burst 1 \
	  --invalid-rate 0.01

# Start only the simulator service
sim-service:
	docker compose --profile simulator up -d simulator

# Logging commands
log-consumer:
	docker compose logs -f consumer

log-simulator:
	docker compose logs -f simulator

log-all:
	docker compose logs -f

# Resource monitoring
stats:
	docker stats --no-stream

monitor:
	@echo "=== Docker Stats ==="
	docker stats --no-stream
	@echo ""
	@echo "=== Service Health ==="
	docker compose ps
	@echo ""
	@echo "=== Redis Stats ==="
	python redis_query.py --stats

# Redis utilities
redis-cli:
	docker compose exec redis redis-cli

redis-stats:
	python redis_query.py --stats

redis-cleanup:
	python redis_query.py --cleanup
