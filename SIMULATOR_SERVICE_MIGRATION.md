# Simulator Service Migration

## Problem Solved

Previously, the simulator ran either locally or would be bundled inside the consumer container, creating issues with:
- Resource isolation and monitoring
- Independent scaling
- Capacity planning and performance analysis

## Solution Implemented

### 1. Separate Simulator Service
- **New Dockerfile**: `Dockerfile.simulator` with minimal dependencies
- **Dedicated Requirements**: `requirements.simulator.txt` (only paho-mqtt, ujson, protobuf)
- **Independent Container**: Runs in its own container with isolated resources

### 2. Resource Management
- **Consumer Limits**: 1 CPU core, 512MB RAM (reserved: 0.5 CPU, 256MB RAM)
- **Simulator Limits**: 0.5 CPU cores, 256MB RAM (reserved: 0.25 CPU, 128MB RAM)
- **Health Checks**: Both services monitor MQTT connectivity

### 3. Flexible Deployment Options
- **Core Services Only**: `make up` (mosquitto + redis + consumer)
- **With Simulator**: `make up-with-sim` (adds simulator service)
- **With Monitoring**: `make up-monitoring` (adds Portainer dashboard)

### 4. Enhanced Monitoring
- **Separate Logs**: `make log-consumer`, `make log-simulator`, `make log-all`
- **Resource Stats**: `make stats` for Docker resource usage
- **Comprehensive**: `make monitor` for health + stats + Redis info
- **Optional Dashboard**: Portainer at http://localhost:9000

### 5. Configuration Management
- **Environment Variables**: Configure simulator behavior via env vars
- **Docker Profiles**: Use profiles to control which services start
- **Example Config**: `.env.example` template for easy setup

## Usage Examples

### Start Everything with Monitoring
```bash
make up-monitoring
```

### Start Core + Simulator Only
```bash
make up-with-sim
```

### Monitor Resource Usage
```bash
make monitor
```

### Scale Simulator Independently
```bash
docker compose up -d --scale simulator=3
```

### Custom Simulator Configuration
```bash
# Create .env file
SIMULATOR_DEVICES=1000
SIMULATOR_RATE=5.0
SIMULATOR_INVALID_RATE=0.02

# Start with custom config
make up-with-sim
```

## Benefits Achieved

1. **Resource Isolation**: Can now monitor CPU/memory usage of simulator vs consumer separately
2. **Independent Scaling**: Scale simulator instances without affecting consumer
3. **Better Debugging**: Separate logs and health checks for each service
4. **Flexible Testing**: Easy to test different simulator configurations
5. **Production Ready**: Resource limits prevent one service from affecting others
6. **Monitoring**: Built-in health checks and optional dashboard for comprehensive monitoring

## Migration Path

- **Existing Users**: Can continue using `make sim` for local simulator
- **New Deployments**: Use `make up-with-sim` for containerized approach
- **Production**: Use `make up-monitoring` for full observability stack
