# Example environment configuration file
# Copy this to .env and modify as needed

# Simulator Configuration
SIMULATOR_DEVICES=100
SIMULATOR_RATE=2.0
SIMULATOR_INVALID_RATE=0.01
MQTT_TOPIC_ROOT=device/messages

# Consumer Configuration
MQTT_HOST=mosquitto
MQTT_PORT=1883
MQTT_TOPIC=device/messages/#
USE_PROTOBUF=true
LOG_LEVEL=INFO
REPORT_EVERY=5

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_TTL=3600
# REDIS_PASSWORD=your_password_here
